import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, SafeAreaView } from 'react-native';
import ThemedText from './ThemedText';
import ThemedView from './ThemedView';
import { shadowPresets } from '../utils/shadowUtils';

const ProfileSidebar = ({ visible, onClose }) => {
  if (!visible) return null;

  return (
    <SafeAreaView style={styles.overlay}>
      <TouchableOpacity style={styles.backdrop} onPress={onClose} />
      <View style={[styles.sidebar, { backgroundColor: '#FFF8F3' }]}>
        <View style={styles.gradientOverlay}>
          {/* 用户信息卡片 */}
          <View style={styles.card}>
            <Image source={require('../assets/Community_image/AvatarOne.png')} style={styles.avatar} />
            <Text style={styles.name}>Jessica</Text>

            <View style={styles.tags}>
              <Text style={styles.tag}>♀ 19岁</Text>
              <Text style={styles.tag}>浙江嘉兴</Text>
            </View>

            <View style={styles.stats}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>118</Text>
                <Text style={styles.statLabel}>关注</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>67</Text>
                <Text style={styles.statLabel}>粉丝</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>73</Text>
                <Text style={styles.statLabel}>获赞与收藏</Text>
              </View>
            </View>
          </View>

          {/* 功能菜单 */}
          <View style={styles.menu}>
            <TouchableOpacity style={styles.menuTextItem}>
              <Image source={require('../assets/Community_image/Star.png')} style={styles.menuIcon} />
              <Text style={styles.menuTextOnly}>社区等级</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.menuTextItem}>
              <Image source={require('../assets/Community_image/Creat.png')} style={styles.menuIcon} />
              <Text style={styles.menuTextOnly}>创作者中心</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.menuTextItem}>
              <Image source={require('../assets/Community_image/Publish.png')} style={styles.menuIcon} />
              <Text style={styles.menuTextOnly}>我发布的</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.menuTextItem}>
              <Image source={require('../assets/Community_image/Star.png')} style={styles.menuIcon} />
              <Text style={styles.menuTextOnly}>我的收藏</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.menuTextItem}>
              <Image source={require('../assets/Community_image/Private.png')} style={styles.menuIcon} />
              <Text style={styles.menuTextOnly}>隐私设置</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.menuTextItem}>
              <Image source={require('../assets/Community_image/Practise.png')} style={styles.menuIcon} />
              <Text style={styles.menuTextOnly}>我的练习</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,

  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',

  },
  sidebar: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '80%',
    height: '100%',
    paddingTop: 60,
    paddingHorizontal: 16,
    overflow: 'hidden',
    backgroundColor: '#FFF8F3',
    ...shadowPresets.sidebar,

  },
  gradientOverlay: {
    flex: 1,
    paddingTop: 60,
    paddingHorizontal: 16,
    backgroundColor: 'transparent',
  },
  card: {
    backgroundColor: '#FFDDB4',
    borderRadius: 20,
    padding: 16,
    alignItems: 'center',
    marginBottom: 32,
    ...shadowPresets.card,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 10,
    resizeMode: 'cover',
  },
  name: {
    fontSize: 20,
    fontWeight: '600',
    color: '#222',
  },
  tags: {
    flexDirection: 'row',
    marginTop: 8,
  },
  tag: {
    backgroundColor: '#FFF',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    fontSize: 12,
    marginHorizontal: 4,
    color: '#666',
  },
  stats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
    width: '100%',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#222',
  },
  statLabel: {
    fontSize: 12,
    color: '#555',
  },
  menu: {
    flex: 1,
  },
  menuTextItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    marginVertical: 2,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  menuIcon: {
    width: 20,
    height: 20,
    marginRight: 12,
    resizeMode: 'contain',
  },
  menuTextOnly: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
});

export default ProfileSidebar;
